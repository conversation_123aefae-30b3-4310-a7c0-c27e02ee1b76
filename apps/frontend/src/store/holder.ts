import type { TopHolderList } from '@silk/types';

import { defineStore } from 'pinia';

const EXPIRE_MS = 24 * 60 * 60 * 1000;

export const useHolderStore = defineStore('holder', {
  state: () => ({
    topHolderList: {} as Record<string, { data: TopHolderList[]; ts: number }>,
  }),
  actions: {
    async addTopHolder(pairAddress: string, topHolder: TopHolderList[]) {
      this.topHolderList[pairAddress] = {
        data: topHolder,
        ts: Date.now(),
      };
    },
    getTopHolders(pairAddress: string): TopHolderList[] {
      const entry = this.topHolderList[pairAddress];
      if (!entry) return [];
      if (Date.now() - entry.ts > EXPIRE_MS) {
        const { [pairAddress]: _, ...rest } = this.topHolderList;
        this.topHolderList = { ...rest };
        return [];
      }
      return entry.data;
    },
  },
  persist: {
    pick: ['topHolderList'],
  },
});
