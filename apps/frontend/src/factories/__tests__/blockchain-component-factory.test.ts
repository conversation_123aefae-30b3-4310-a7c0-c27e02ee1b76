import { describe, it, expect, beforeEach, vi } from 'vitest';
import { defineComponent } from 'vue';
import { BlockchainComponentFactory } from '../blockchain-component-factory';
import { 
  BlockchainType,
  ComponentFactoryError,
  ComponentNotRegisteredError,
  ComponentLoadError 
} from '../../types';

// Mock components for testing
const MockEthereumWalletConnect = defineComponent({
  name: 'MockEthereumWalletConnect',
  template: '<div>Ethereum Wallet Connect</div>',
});

const MockSolanaWalletConnect = defineComponent({
  name: 'MockSolanaWalletConnect', 
  template: '<div>Solana Wallet Connect</div>',
});

const MockEthereumTransactionList = defineComponent({
  name: 'MockEthereumTransactionList',
  template: '<div>Ethereum Transaction List</div>',
});

describe('BlockchainComponentFactory', () => {
  let factory: BlockchainComponentFactory;

  beforeEach(() => {
    // Get a fresh instance and clear all components for each test
    factory = BlockchainComponentFactory.getInstance();
    factory.clearAllComponents();
  });

  describe('Singleton Pattern', () => {
    it('should return the same instance when called multiple times', () => {
      const instance1 = BlockchainComponentFactory.getInstance();
      const instance2 = BlockchainComponentFactory.getInstance();
      
      expect(instance1).toBe(instance2);
    });

    it('should maintain state across getInstance calls', () => {
      const instance1 = BlockchainComponentFactory.getInstance();
      instance1.registerComponent(
        BlockchainType.ETHEREUM,
        'TestComponent',
        () => Promise.resolve(MockEthereumWalletConnect)
      );

      const instance2 = BlockchainComponentFactory.getInstance();
      expect(instance2.hasComponent(BlockchainType.ETHEREUM, 'TestComponent')).toBe(true);
    });
  });

  describe('Component Registration', () => {
    it('should register a component successfully', () => {
      factory.registerComponent(
        BlockchainType.ETHEREUM,
        'WalletConnect',
        () => Promise.resolve(MockEthereumWalletConnect)
      );

      expect(factory.hasComponent(BlockchainType.ETHEREUM, 'WalletConnect')).toBe(true);
    });

    it('should register multiple components for the same blockchain', () => {
      factory.registerComponent(
        BlockchainType.ETHEREUM,
        'WalletConnect',
        () => Promise.resolve(MockEthereumWalletConnect)
      );

      factory.registerComponent(
        BlockchainType.ETHEREUM,
        'TransactionList',
        () => Promise.resolve(MockEthereumTransactionList)
      );

      expect(factory.hasComponent(BlockchainType.ETHEREUM, 'WalletConnect')).toBe(true);
      expect(factory.hasComponent(BlockchainType.ETHEREUM, 'TransactionList')).toBe(true);
    });

    it('should register components for different blockchains', () => {
      factory.registerComponent(
        BlockchainType.ETHEREUM,
        'WalletConnect',
        () => Promise.resolve(MockEthereumWalletConnect)
      );

      factory.registerComponent(
        BlockchainType.SOLANA,
        'WalletConnect',
        () => Promise.resolve(MockSolanaWalletConnect)
      );

      expect(factory.hasComponent(BlockchainType.ETHEREUM, 'WalletConnect')).toBe(true);
      expect(factory.hasComponent(BlockchainType.SOLANA, 'WalletConnect')).toBe(true);
    });

    it('should overwrite existing component when registering with same name', () => {
      const originalLoader = () => Promise.resolve(MockEthereumWalletConnect);
      const newLoader = () => Promise.resolve(MockEthereumTransactionList);

      factory.registerComponent(BlockchainType.ETHEREUM, 'WalletConnect', originalLoader);
      factory.registerComponent(BlockchainType.ETHEREUM, 'WalletConnect', newLoader);

      expect(factory.hasComponent(BlockchainType.ETHEREUM, 'WalletConnect')).toBe(true);
    });

    it('should throw error when blockchain type is not provided', () => {
      expect(() => {
        factory.registerComponent(
          '' as BlockchainType,
          'WalletConnect',
          () => Promise.resolve(MockEthereumWalletConnect)
        );
      }).toThrow(ComponentFactoryError);
    });

    it('should throw error when component name is empty', () => {
      expect(() => {
        factory.registerComponent(
          BlockchainType.ETHEREUM,
          '',
          () => Promise.resolve(MockEthereumWalletConnect)
        );
      }).toThrow(ComponentFactoryError);
    });

    it('should throw error when component name is only whitespace', () => {
      expect(() => {
        factory.registerComponent(
          BlockchainType.ETHEREUM,
          '   ',
          () => Promise.resolve(MockEthereumWalletConnect)
        );
      }).toThrow(ComponentFactoryError);
    });

    it('should throw error when component is not a function', () => {
      expect(() => {
        factory.registerComponent(
          BlockchainType.ETHEREUM,
          'WalletConnect',
          'not-a-function' as any
        );
      }).toThrow(ComponentFactoryError);
    });
  });

  describe('Component Checking', () => {
    beforeEach(() => {
      factory.registerComponent(
        BlockchainType.ETHEREUM,
        'WalletConnect',
        () => Promise.resolve(MockEthereumWalletConnect)
      );
    });

    it('should return true for registered component', () => {
      expect(factory.hasComponent(BlockchainType.ETHEREUM, 'WalletConnect')).toBe(true);
    });

    it('should return false for unregistered component', () => {
      expect(factory.hasComponent(BlockchainType.ETHEREUM, 'UnregisteredComponent')).toBe(false);
    });

    it('should return false for component registered under different blockchain', () => {
      expect(factory.hasComponent(BlockchainType.SOLANA, 'WalletConnect')).toBe(false);
    });

    it('should return false for unsupported blockchain type', () => {
      expect(factory.hasComponent('bitcoin' as BlockchainType, 'WalletConnect')).toBe(false);
    });
  });

  describe('Component Retrieval', () => {
    beforeEach(() => {
      factory.registerComponent(
        BlockchainType.ETHEREUM,
        'WalletConnect',
        () => Promise.resolve(MockEthereumWalletConnect)
      );

      factory.registerComponent(
        BlockchainType.SOLANA,
        'WalletConnect',
        () => Promise.resolve(MockSolanaWalletConnect)
      );
    });

    it('should successfully retrieve registered component', async () => {
      const component = await factory.getComponent(BlockchainType.ETHEREUM, 'WalletConnect');
      expect(component).toBe(MockEthereumWalletConnect);
    });

    it('should retrieve correct component for different blockchains', async () => {
      const ethereumComponent = await factory.getComponent(BlockchainType.ETHEREUM, 'WalletConnect');
      const solanaComponent = await factory.getComponent(BlockchainType.SOLANA, 'WalletConnect');

      expect(ethereumComponent).toBe(MockEthereumWalletConnect);
      expect(solanaComponent).toBe(MockSolanaWalletConnect);
    });

    it('should throw ComponentNotRegisteredError for unregistered component', async () => {
      await expect(
        factory.getComponent(BlockchainType.ETHEREUM, 'UnregisteredComponent')
      ).rejects.toThrow(ComponentNotRegisteredError);
    });

    it('should throw error when blockchain type is not provided', async () => {
      await expect(
        factory.getComponent('' as BlockchainType, 'WalletConnect')
      ).rejects.toThrow(ComponentFactoryError);
    });

    it('should throw error when component name is empty', async () => {
      await expect(
        factory.getComponent(BlockchainType.ETHEREUM, '')
      ).rejects.toThrow(ComponentFactoryError);
    });

    it('should throw ComponentLoadError when component loader fails', async () => {
      const failingLoader = () => Promise.reject(new Error('Loading failed'));
      
      factory.registerComponent(
        BlockchainType.ETHEREUM,
        'FailingComponent',
        failingLoader
      );

      await expect(
        factory.getComponent(BlockchainType.ETHEREUM, 'FailingComponent')
      ).rejects.toThrow(ComponentLoadError);
    });

    it('should throw ComponentLoadError when component loader returns null', async () => {
      const nullLoader = () => Promise.resolve(null as any);
      
      factory.registerComponent(
        BlockchainType.ETHEREUM,
        'NullComponent',
        nullLoader
      );

      await expect(
        factory.getComponent(BlockchainType.ETHEREUM, 'NullComponent')
      ).rejects.toThrow(ComponentLoadError);
    });
  });

  describe('Utility Methods', () => {
    beforeEach(() => {
      factory.registerComponent(
        BlockchainType.ETHEREUM,
        'WalletConnect',
        () => Promise.resolve(MockEthereumWalletConnect)
      );

      factory.registerComponent(
        BlockchainType.ETHEREUM,
        'TransactionList',
        () => Promise.resolve(MockEthereumTransactionList)
      );

      factory.registerComponent(
        BlockchainType.SOLANA,
        'WalletConnect',
        () => Promise.resolve(MockSolanaWalletConnect)
      );
    });

    describe('getRegisteredComponents', () => {
      it('should return list of registered components for blockchain', () => {
        const ethereumComponents = factory.getRegisteredComponents(BlockchainType.ETHEREUM);
        const solanaComponents = factory.getRegisteredComponents(BlockchainType.SOLANA);

        expect(ethereumComponents).toEqual(['WalletConnect', 'TransactionList']);
        expect(solanaComponents).toEqual(['WalletConnect']);
      });

      it('should return empty array for blockchain with no components', () => {
        factory.clearAllComponents();
        const components = factory.getRegisteredComponents(BlockchainType.ETHEREUM);
        expect(components).toEqual([]);
      });

      it('should return empty array for unsupported blockchain', () => {
        const components = factory.getRegisteredComponents('bitcoin' as BlockchainType);
        expect(components).toEqual([]);
      });
    });

    describe('getSupportedBlockchains', () => {
      it('should return all supported blockchain types', () => {
        const supportedBlockchains = factory.getSupportedBlockchains();
        expect(supportedBlockchains).toEqual([BlockchainType.ETHEREUM, BlockchainType.SOLANA]);
      });
    });

    describe('getStats', () => {
      it('should return correct component count statistics', () => {
        const stats = factory.getStats();
        
        expect(stats).toEqual({
          [BlockchainType.ETHEREUM]: 2,
          [BlockchainType.SOLANA]: 1,
        });
      });

      it('should return zero counts after clearing components', () => {
        factory.clearAllComponents();
        const stats = factory.getStats();
        
        expect(stats).toEqual({
          [BlockchainType.ETHEREUM]: 0,
          [BlockchainType.SOLANA]: 0,
        });
      });
    });

    describe('clearAllComponents', () => {
      it('should clear all registered components', () => {
        expect(factory.hasComponent(BlockchainType.ETHEREUM, 'WalletConnect')).toBe(true);
        expect(factory.hasComponent(BlockchainType.SOLANA, 'WalletConnect')).toBe(true);

        factory.clearAllComponents();

        expect(factory.hasComponent(BlockchainType.ETHEREUM, 'WalletConnect')).toBe(false);
        expect(factory.hasComponent(BlockchainType.SOLANA, 'WalletConnect')).toBe(false);
      });

      it('should reset component counts to zero', () => {
        factory.clearAllComponents();
        const stats = factory.getStats();
        
        expect(stats[BlockchainType.ETHEREUM]).toBe(0);
        expect(stats[BlockchainType.SOLANA]).toBe(0);
      });
    });
  });

  describe('Error Handling', () => {
    it('should provide meaningful error messages', async () => {
      try {
        await factory.getComponent(BlockchainType.ETHEREUM, 'NonExistentComponent');
      } catch (error) {
        expect(error).toBeInstanceOf(ComponentNotRegisteredError);
        expect((error as ComponentNotRegisteredError).message).toContain('NonExistentComponent');
        expect((error as ComponentNotRegisteredError).message).toContain('ethereum');
        expect((error as ComponentNotRegisteredError).blockchainType).toBe(BlockchainType.ETHEREUM);
        expect((error as ComponentNotRegisteredError).componentName).toBe('NonExistentComponent');
      }
    });

    it('should preserve original error in ComponentLoadError', async () => {
      const originalError = new Error('Network timeout');
      const failingLoader = () => Promise.reject(originalError);
      
      factory.registerComponent(
        BlockchainType.ETHEREUM,
        'FailingComponent',
        failingLoader
      );

      try {
        await factory.getComponent(BlockchainType.ETHEREUM, 'FailingComponent');
      } catch (error) {
        expect(error).toBeInstanceOf(ComponentLoadError);
        expect((error as ComponentLoadError).cause).toBe(originalError);
        expect((error as ComponentLoadError).message).toContain('Network timeout');
      }
    });
  });

  describe('Console Logging', () => {
    beforeEach(() => {
      vi.spyOn(console, 'debug').mockImplementation(() => {});
      vi.spyOn(console, 'error').mockImplementation(() => {});
    });

    it('should log component registration', () => {
      factory.registerComponent(
        BlockchainType.ETHEREUM,
        'WalletConnect',
        () => Promise.resolve(MockEthereumWalletConnect)
      );

      expect(console.debug).toHaveBeenCalledWith(
        '[BlockchainComponentFactory] Registered component "WalletConnect" for blockchain "ethereum"'
      );
    });

    it('should log component loading', async () => {
      factory.registerComponent(
        BlockchainType.ETHEREUM,
        'WalletConnect',
        () => Promise.resolve(MockEthereumWalletConnect)
      );

      await factory.getComponent(BlockchainType.ETHEREUM, 'WalletConnect');

      expect(console.debug).toHaveBeenCalledWith(
        '[BlockchainComponentFactory] Loading component "WalletConnect" for blockchain "ethereum"'
      );

      expect(console.debug).toHaveBeenCalledWith(
        '[BlockchainComponentFactory] Successfully loaded component "WalletConnect" for blockchain "ethereum"'
      );
    });

    it('should log component loading errors', async () => {
      const failingLoader = () => Promise.reject(new Error('Loading failed'));
      
      factory.registerComponent(
        BlockchainType.ETHEREUM,
        'FailingComponent',
        failingLoader
      );

      try {
        await factory.getComponent(BlockchainType.ETHEREUM, 'FailingComponent');
      } catch (error) {
        expect(console.error).toHaveBeenCalledWith(
          '[BlockchainComponentFactory] Failed to load component "FailingComponent" for blockchain "ethereum"',
          expect.any(ComponentLoadError)
        );
      }
    });

    it('should log when clearing all components', () => {
      factory.clearAllComponents();

      expect(console.debug).toHaveBeenCalledWith(
        '[BlockchainComponentFactory] Cleared all registered components'
      );
    });
  });
});
