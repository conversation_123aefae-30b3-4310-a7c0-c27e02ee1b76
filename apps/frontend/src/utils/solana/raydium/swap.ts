import Decimal from 'decimal.js';

import {
  getSolanaWallet,
  makeRaydiumAmmSwapInstruction,
  poolKeysCache,
} from '@/utils';
import { BN } from '@coral-xyz/anchor';
import {
  type AmmRpcData,
  type ApiV3PoolInfoConcentratedItem,
  type ApiV3PoolInfoStandardItemCpmm,
  type ClmmKeys,
  type ComputeClmmPoolInfo,
  type CpmmKeys,
  type CpmmRpcData,
  Curve,
  CurveCalculator,
  LAUNCHPAD_PROGRAM,
  type LaunchpadConfigInfo,
  type LaunchpadPlatformInfo,
  type LaunchpadPoolInfo,
  PoolUtils,
  Raydium,
  type ReturnTypeFetchMultiplePoolTickArrays,
  type SwapResult,
  toAmmComputePoolInfo,
  TxVersion,
  USDCMint,
  USDTMint,
} from '@raydium-io/raydium-sdk-v2';
import { NATIVE_MINT } from '@solana/spl-token';
import {
  Connection,
  type GetProgramAccountsResponse,
  LAMPORTS_PER_SOL,
  PublicKey,
} from '@solana/web3.js';

export async function initRaydium(
  connection: Connection,
  privateKey?: string,
): Promise<Raydium> {
  const wallet = privateKey ? getSolanaWallet(privateKey) : undefined;
  const raydium = await Raydium.load({
    connection,
    disableFeatureCheck: true,
    disableLoadToken: true,
    logRequests: false,
    blockhashCommitment: 'confirmed',
    owner: wallet,
  });
  return raydium;
}

export function raydiumAmmPoolCalculate(
  raydium: Raydium,
  poolRpcData: AmmRpcData,
  isBuy: boolean,
  sendAmount: number,
  slippage: number,
) {
  const {
    marketId,
    baseReserve,
    quoteReserve,
    status,
    baseMint,
    quoteMint,
    baseDecimal,
    quoteDecimal,
  } = poolRpcData;
  const poolId = marketId.toString();
  const poolInfo = toAmmComputePoolInfo({ [poolId]: poolRpcData })[poolId];
  if (!poolInfo) return;
  const isPumpfun = poolRpcData.baseMint.toString() === NATIVE_MINT.toString();
  let currentAmountIn, currentMintIn, currentMintOut, decimal;

  if (isPumpfun) {
    currentMintIn = isBuy ? baseMint : quoteMint;
    currentMintOut = isBuy ? quoteMint : baseMint;
    currentAmountIn = isBuy
      ? new BN(sendAmount * LAMPORTS_PER_SOL)
      : new BN(sendAmount * 10 ** quoteDecimal.toNumber());
    decimal = isBuy ? quoteDecimal.toNumber() : baseDecimal.toNumber();
  } else {
    currentMintIn = isBuy ? quoteMint : baseMint;
    currentMintOut = isBuy ? baseMint : quoteMint;
    currentAmountIn = isBuy
      ? new BN(sendAmount * LAMPORTS_PER_SOL)
      : new BN(sendAmount * 10 ** baseDecimal.toNumber());
    decimal = isBuy ? baseDecimal.toNumber() : quoteDecimal.toNumber();
  }

  const out = raydium.liquidity.computeAmountOut({
    poolInfo: {
      ...poolInfo,
      baseReserve,
      quoteReserve,
      status: status.toNumber(),
      version: 4,
    },
    amountIn: currentAmountIn,
    mintIn: currentMintIn,
    mintOut: currentMintOut,
    slippage: slippage / 100,
  });

  if (!out) return;

  const formatPrice = (price: Decimal) => {
    const priceNumber = Number(price.toFixed(8));
    if (priceNumber < 0.000_001) {
      return priceNumber.toExponential(6);
    }
    return priceNumber;
  };

  const formatPriceImpact = (impact: Decimal) => {
    return Number(impact.mul(100).toFixed(2));
  };

  return {
    amountOut: out.amountOut.toNumber() / 10 ** decimal,
    minAmountOut: out.minAmountOut.toNumber() / 10 ** decimal,
    currentPrice: formatPrice(out.currentPrice),
    executionPrice: formatPrice(out.executionPrice),
    priceImpact: formatPriceImpact(out.priceImpact),
  };
}

interface RaydiumSwapParams {
  raydium: Raydium;
  poolRpcData: AmmRpcData;
  pairAddress: string;
  tokenAccount: GetProgramAccountsResponse | undefined;
  minCreateAccountBalanceNeed: number;
  isBuy: boolean;
  amountIn: number;
  minAmountOut: number;
}

export async function raydiumAmmSwap({
  raydium,
  poolRpcData,
  pairAddress,
  tokenAccount,
  minCreateAccountBalanceNeed,
  isBuy,
  amountIn,
  minAmountOut,
}: RaydiumSwapParams) {
  const poolKeys = poolKeysCache.get(pairAddress);
  if (!poolKeys) throw new Error('Pool keys not found');

  const { baseMint, quoteMint, baseDecimal, quoteDecimal } = poolRpcData;
  const isPumpfun = baseMint.toString() === NATIVE_MINT.toString();
  let currentAmountIn, currentMinAmountOut, currentMint;

  if (isPumpfun) {
    currentMint = quoteMint;
    currentAmountIn = isBuy
      ? new BN(amountIn * LAMPORTS_PER_SOL)
      : new BN(amountIn * 10 ** quoteDecimal.toNumber());
    currentMinAmountOut = isBuy
      ? new BN(minAmountOut * 10 ** quoteDecimal.toNumber())
      : new BN(minAmountOut * LAMPORTS_PER_SOL);
  } else {
    currentMint = baseMint;
    currentAmountIn = isBuy
      ? new BN(amountIn * LAMPORTS_PER_SOL)
      : new BN(amountIn * 10 ** baseDecimal.toNumber());
    currentMinAmountOut = isBuy
      ? new BN(minAmountOut * 10 ** baseDecimal.toNumber())
      : new BN(minAmountOut * LAMPORTS_PER_SOL);
  }

  const ammSwapInstruction = await makeRaydiumAmmSwapInstruction(
    poolKeys,
    isBuy,
    tokenAccount,
    raydium.ownerPubKey,
    currentMint,
    currentAmountIn,
    currentMinAmountOut,
    minCreateAccountBalanceNeed,
  );
  if (!ammSwapInstruction) throw new Error('Failed to create swap instruction');
  return ammSwapInstruction;
}

export async function raydiumClmmPoolCalculate(
  raydium: Raydium,
  poolInfo: ApiV3PoolInfoConcentratedItem,
  clmmPoolInfo: ComputeClmmPoolInfo,
  tickCache: ReturnTypeFetchMultiplePoolTickArrays,
  pairAddress: string,
  isBuy: boolean,
  sendAmount: number,
  slippage: number,
) {
  const { mintA, mintB } = poolInfo;
  let currentAmountIn, currentTokenOut, decimal;
  const isUSD =
    mintB.address === USDCMint.toString() ||
    mintB.address === USDTMint.toString();

  if (isBuy) {
    currentAmountIn = isUSD
      ? new BN(sendAmount * 10 ** mintB.decimals)
      : new BN(sendAmount * 10 ** mintA.decimals);
    currentTokenOut = isUSD ? poolInfo.mintA : poolInfo.mintB;
    decimal = isUSD ? mintA.decimals : mintB.decimals;
  } else {
    currentAmountIn = isUSD
      ? new BN(sendAmount * 10 ** mintA.decimals)
      : new BN(sendAmount * 10 ** mintB.decimals);
    currentTokenOut = isUSD ? poolInfo.mintB : poolInfo.mintA;
    decimal = isUSD ? mintB.decimals : mintA.decimals;
  }

  const { amountOut, minAmountOut, remainingAccounts } =
    PoolUtils.computeAmountOutFormat({
      poolInfo: clmmPoolInfo,
      tickArrayCache: tickCache[pairAddress] ?? {},
      amountIn: currentAmountIn,
      tokenOut: currentTokenOut,
      slippage: slippage / 100,
      epochInfo: await raydium.fetchEpochInfo(),
    });

  return {
    amountOut: amountOut.amount.raw.toNumber() / 10 ** decimal,
    minAmountOut: minAmountOut.amount.raw.toNumber() / 10 ** decimal,
    remainingAccounts,
  };
}

export async function raydiumClmmSwap({
  raydium,
  poolInfo,
  poolKeys,
  isBuy,
  sendAmount,
  minAmountOut,
  remainingAccounts,
}: {
  isBuy: boolean;
  minAmountOut: number;
  poolInfo: ApiV3PoolInfoConcentratedItem;
  poolKeys: ClmmKeys;
  raydium: Raydium;
  remainingAccounts: any;
  sendAmount: number;
}) {
  let currentAmountIn, currentMinAmountOut, inputMint;
  const { mintA, mintB } = poolInfo;
  const isUSD =
    mintB.address === USDCMint.toString() ||
    mintB.address === USDTMint.toString();

  if (isBuy) {
    currentAmountIn = isUSD
      ? new BN(sendAmount * 10 ** mintB.decimals)
      : new BN(sendAmount * 10 ** mintA.decimals);
    currentMinAmountOut = isUSD
      ? new BN(minAmountOut * 10 ** mintA.decimals)
      : new BN(minAmountOut * 10 ** mintB.decimals);
    inputMint = isUSD ? mintB : mintA;
  } else {
    currentAmountIn = isUSD
      ? new BN(sendAmount * 10 ** mintA.decimals)
      : new BN(sendAmount * 10 ** mintB.decimals);
    currentMinAmountOut = isUSD
      ? new BN(minAmountOut * 10 ** mintB.decimals)
      : new BN(minAmountOut * 10 ** mintA.decimals);
    inputMint = isUSD ? mintA : mintB;
  }

  const { builder } = await raydium.clmm.swap({
    poolInfo,
    poolKeys,
    inputMint: inputMint.address,
    amountIn: currentAmountIn,
    amountOutMin: currentMinAmountOut,
    observationId: new PublicKey(poolKeys.observationId),
    ownerInfo: {
      useSOLBalance: true,
    },
    remainingAccounts,
    txVersion: TxVersion.V0,
  });
  const instructions = builder.AllTxData.instructions;
  const endInstructions = builder.AllTxData.endInstructions;
  return [...instructions, ...endInstructions];
}

export function raydiumCpmmPoolCalculate(
  poolInfo: ApiV3PoolInfoStandardItemCpmm,
  rpcData: CpmmRpcData,
  isBuy: boolean,
  sendAmount: number,
) {
  let decimals, destinationReserve, outPutDecimals, sourceReserve;
  const { mintA, mintB } = poolInfo;

  if (!rpcData.configInfo) return;

  if (
    mintA.address === NATIVE_MINT.toString() ||
    mintA.address === USDCMint.toString() ||
    mintA.address === USDTMint.toString()
  ) {
    if (isBuy) {
      sourceReserve = rpcData.baseReserve;
      destinationReserve = rpcData.quoteReserve;
      decimals = mintA.decimals;
      outPutDecimals = mintB.decimals;
    } else {
      sourceReserve = rpcData.quoteReserve;
      destinationReserve = rpcData.baseReserve;
      decimals = mintB.decimals;
      outPutDecimals = mintA.decimals;
    }
  } else {
    if (isBuy) {
      sourceReserve = rpcData.quoteReserve;
      destinationReserve = rpcData.baseReserve;
      decimals = mintB.decimals;
      outPutDecimals = mintA.decimals;
    } else {
      sourceReserve = rpcData.baseReserve;
      destinationReserve = rpcData.quoteReserve;
      decimals = mintA.decimals;
      outPutDecimals = mintB.decimals;
    }
  }

  const currentAmountIn = new BN(sendAmount * 10 ** decimals);

  const swapResult = CurveCalculator.swap(
    currentAmountIn,
    sourceReserve,
    destinationReserve,
    rpcData.configInfo.tradeFeeRate,
  );

  return {
    swapResult,
    amountOut:
      swapResult.destinationAmountSwapped.toNumber() / 10 ** outPutDecimals,
    minAmountOut:
      swapResult.destinationAmountSwapped.toNumber() / 10 ** outPutDecimals,
  };
}

export async function raydiumCpmmSwap({
  raydium,
  poolInfo,
  poolKeys,
  swapResult,
  isBuy,
  sendAmount,
  slippage,
}: {
  isBuy: boolean;
  poolInfo: ApiV3PoolInfoStandardItemCpmm;
  poolKeys: CpmmKeys;
  raydium: Raydium;
  sendAmount: number;
  slippage: number;
  swapResult: SwapResult;
}) {
  let decimals, isBaseIn;
  const { mintA, mintB } = poolInfo;

  if (
    mintA.address === NATIVE_MINT.toString() ||
    mintA.address === USDCMint.toString() ||
    mintA.address === USDTMint.toString()
  ) {
    decimals = isBuy ? mintA.decimals : mintB.decimals;
    isBaseIn = isBuy;
  } else {
    decimals = isBuy ? mintB.decimals : mintA.decimals;
    isBaseIn = !isBuy;
  }

  const currentAmountIn = new BN(sendAmount * 10 ** decimals);

  const { builder } = await raydium.cpmm.swap({
    poolInfo,
    poolKeys,
    inputAmount: currentAmountIn,
    swapResult,
    slippage: slippage / 100,
    baseIn: isBaseIn,
  });

  const instructions = builder.AllTxData.instructions;
  const endInstructions = builder.AllTxData.endInstructions;

  return [...instructions, ...endInstructions];
}

export function raydiumLaunchPadPoolCalculate(
  poolInfo: { configInfo: LaunchpadConfigInfo } & LaunchpadPoolInfo,
  platformInfo: LaunchpadPlatformInfo,
  isBuy: boolean,
  sendAmount: number,
  slippage: number,
) {
  const { mintDecimalsA, mintDecimalsB } = poolInfo;
  const decimal = isBuy ? mintDecimalsA : mintDecimalsB;

  const currentAmountIn = isBuy
    ? new BN(sendAmount * 10 ** mintDecimalsB)
    : new BN(sendAmount * 10 ** mintDecimalsA);

  const curveResult = isBuy
    ? Curve.buyExactIn({
        poolInfo,
        amountB: currentAmountIn,
        protocolFeeRate: poolInfo.configInfo.tradeFeeRate,
        platformFeeRate: platformInfo.feeRate,
        curveType: poolInfo.configInfo.curveType,
        shareFeeRate: new BN(0),
      })
    : Curve.sellExactIn({
        poolInfo,
        amountA: currentAmountIn,
        protocolFeeRate: poolInfo.configInfo.tradeFeeRate,
        platformFeeRate: platformInfo.feeRate,
        curveType: poolInfo.configInfo.curveType,
        shareFeeRate: new BN(0),
      });

  const amountOut = isBuy
    ? curveResult.amountA.toNumber() / 10 ** decimal
    : curveResult.amountB.toNumber() / 10 ** decimal;

  const minAmountOut = isBuy
    ? new Decimal(curveResult.amountA.toString())
        .mul((10_000 - slippage * 100) / 10_000)
        .toNumber() /
      10 ** decimal
    : new Decimal(curveResult.amountB.toString())
        .mul((10_000 - slippage * 100) / 10_000)
        .toNumber() /
      10 ** decimal;

  return {
    amountOut,
    minAmountOut,
  };
}

export async function raydiumLaunchPadSwap({
  raydium,
  poolInfo,
  platformInfo,
  isBuy,
  sendAmount,
  slippage,
}: {
  isBuy: boolean;
  platformInfo: LaunchpadPlatformInfo;
  poolInfo: { configInfo: LaunchpadConfigInfo } & LaunchpadPoolInfo;
  raydium: Raydium;
  sendAmount: number;
  slippage: number;
}) {
  const { mintDecimalsA, mintDecimalsB } = poolInfo;

  const currentAmountIn = isBuy
    ? new BN(sendAmount * 10 ** mintDecimalsB)
    : new BN(sendAmount * 10 ** mintDecimalsA);

  const { builder } = isBuy
    ? await raydium.launchpad.buyToken({
        programId: LAUNCHPAD_PROGRAM,
        mintA: poolInfo.mintA,
        feePayer: raydium.ownerPubKey,
        configInfo: poolInfo.configInfo,
        buyAmount: currentAmountIn,
        platformFeeRate: platformInfo.feeRate,
        txVersion: TxVersion.V0,
        slippage: new BN(slippage * 100),
      })
    : await raydium.launchpad.sellToken({
        programId: LAUNCHPAD_PROGRAM,
        mintA: poolInfo.mintA,
        feePayer: raydium.ownerPubKey,
        configInfo: poolInfo.configInfo,
        sellAmount: currentAmountIn,
        platformFeeRate: platformInfo.feeRate,
        txVersion: TxVersion.V0,
        slippage: new BN(slippage * 100),
      });

  const instructions = builder.AllTxData.instructions;
  const endInstructions = builder.AllTxData.endInstructions;

  return [...instructions, ...endInstructions];
}
