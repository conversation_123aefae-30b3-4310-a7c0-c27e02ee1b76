import { createApp } from 'vue';

import { registerAccessDirective } from '@silk/access';
import { registerLoadingDirective } from '@silk/common-ui/es/loading';
import { initStores } from '@silk/stores';
import '@silk/styles';
import '@silk/styles/antd';

import { setupI18n } from '@/locales';

import { initComponentAdapter } from './adapter/component';
import { initSetupSilkForm } from './adapter/form';
import App from './app.vue';
import { router } from './router';

async function bootstrap(namespace: string) {
  await initComponentAdapter();

  await initSetupSilkForm();

  const app = createApp(App);

  registerLoadingDirective(app, {
    loading: 'loading',
    spinning: 'spinning',
  });

  await setupI18n(app);

  await initStores(app, { namespace });

  registerAccessDirective(app);

  app.use(router);

  const { VueQueryPlugin } = await import('@tanstack/vue-query');
  app.use(VueQueryPlugin);

  app.mount('#app');
}

export { bootstrap };
