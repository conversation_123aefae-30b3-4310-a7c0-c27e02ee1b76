<script setup lang="ts">
import type { EthereumTokenInfo, FormattedPoolInfo } from '@silk/types';

import { computed, defineAsyncComponent, ref } from 'vue';

import {
  Badge,
  EthereumTokenPoolInfo,
  SilkIconButton,
  SilkLoading,
  SolanaTokenPoolInfo,
  Switch,
  TokenInfo,
  TransactionCard,
} from '@silk/common-ui';
import { useIsMobile } from '@silk/hooks';
import { RotateCw, Settings } from '@silk/icons';
import { useBotConfigStore, useTradePreferencesStore } from '@silk/stores';

import { storeToRefs } from 'pinia';

import { useMonitorQuery, useSwapView } from '@/composables';
import {
  type RefreshType,
  useRefreshStore,
  useSwapSettingsStore,
} from '@/store';

const props = defineProps<{
  chain: 'ethereum' | 'solana';
  pairAddress: string;
  pool: FormattedPoolInfo;
  tokenInfo?: EthereumTokenInfo;
}>();

const activeTab = defineModel<string>('activeTab');
const tradePreferencesStore = useTradePreferencesStore();
const EthereumSwapTransaction = defineAsyncComponent(
  () => import('./module/transactions/ethereum-swap-transaction.vue'),
);
const SolanaSwapTransaction = defineAsyncComponent(
  () => import('./module/transactions/solana-swap-transaction.vue'),
);

const EthereumHolder = defineAsyncComponent(
  () => import('./module/holder/ethereum-holder.vue'),
);
const SolanaHolder = defineAsyncComponent(
  () => import('./module/holder/solana-holder.vue'),
);

const EthereumProfit = defineAsyncComponent(
  () => import('./module/profit/ethereum-profit.vue'),
);
const SolanaProfit = defineAsyncComponent(
  () => import('./module/profit/solana-profit.vue'),
);

const EthereumTokenManage = defineAsyncComponent(
  () => import('./module/token-manage/ethereum-token-manage.vue'),
);
const SolanaTokenManage = defineAsyncComponent(
  () => import('./module/token-manage/solana-token-manage.vue'),
);

const { data: monitorList, refetch: refetchMonitorList } = useMonitorQuery(
  props.chain,
);

const { currentComponent, tokenData, swapActionProps } = useSwapView();

const isHover = ref(false);
const transactionMaker = ref<string>();

const { isMobile } = useIsMobile();
const swapSettingsStore = useSwapSettingsStore();
const botConfigStore = useBotConfigStore();
const refreshStore = useRefreshStore();
const { swapTransactionSettings } = storeToRefs(botConfigStore);

const holderCountLabel = computed(() => {
  return props.pool.holderInfo?.holderCount
    ? `持有者(${props.pool.holderInfo?.holderCount ?? 0})`
    : `持有者(${props.tokenInfo?.holderCount ?? 0})`;
});

const commonTabs = [
  { label: '最新交易', value: 'transaction' },
  { label: holderCountLabel, value: 'holder' },
  { label: '收益统计', value: 'profit' },
];

const desktopTabs = [
  ...commonTabs,
  {
    label: '持仓管理',
    value: 'tokenManage',
    disabled: props.chain === 'ethereum',
  },
];

const mobileTabs = [
  { label: 'Swap', value: 'swap' },
  {
    label: '快捷交易',
    value: 'quickTrading',
    disabled: props.chain === 'ethereum',
  },
  ...commonTabs,
  { label: '代币信息', value: 'tokenInfo' },
];

const swapTabs = computed(() => (isMobile.value ? mobileTabs : desktopTabs));

const cQuickTrading = computed({
  get: () => {
    return props.chain === 'ethereum'
      ? tradePreferencesStore.ethereumQuickTrading
      : tradePreferencesStore.solanaQuickTrading;
  },
  set: (value: boolean) => {
    if (props.chain === 'ethereum') {
      tradePreferencesStore.setEthereumQuickTrading(value);
    } else {
      tradePreferencesStore.setSolanaQuickTrading(value);
    }
  },
});

const onHolderChange = (holder: string) => {
  transactionMaker.value = holder;
  activeTab.value = 'transaction';
};

const holderRef = ref();

const handleRefresh = () => {
  if (activeTab.value === 'holder') {
    holderRef.value?.refreshHolder();
  } else {
    refreshStore.triggerRefresh(activeTab.value as RefreshType);
  }
};
</script>

<template>
  <TransactionCard
    v-model:model-value="activeTab"
    :tabs="swapTabs"
    class="h-full"
    variant="default"
    @update:model-value="transactionMaker = undefined"
  >
    <template #actions>
      <div
        v-if="chain === 'solana' && !isMobile"
        class="mx-3 flex items-center"
      >
        <span class="px-2 text-xs">地址过滤</span>
        <Switch
          :checked="swapTransactionSettings.filter"
          @update:checked="
            (value) =>
              botConfigStore.setTransactionSettings({
                ...swapTransactionSettings,
                filter: value,
              })
          "
        />
        <span class="px-2 text-xs">快捷交易</span>
        <Switch
          :checked="cQuickTrading"
          @update:checked="
            (value) => {
              if (props.chain === 'ethereum') {
                tradePreferencesStore.setEthereumQuickTrading(value);
              } else {
                tradePreferencesStore.setSolanaQuickTrading(value);
              }
            }
          "
        />
      </div>
      <SilkIconButton
        v-if="['swap', 'quickTrading'].includes(activeTab!)"
        @click="swapSettingsStore.open = true"
      >
        <Settings class="text-foreground size-4" />
      </SilkIconButton>
      <SilkIconButton
        v-else-if="activeTab === 'holder' || activeTab === 'tokenManage'"
        class="my-0 mr-1 rounded-md"
        @click="handleRefresh"
      >
        <RotateCw class="size-4" />
      </SilkIconButton>
      <template v-else-if="activeTab === 'transaction'">
        <template v-if="isMobile">
          <span class="px-1 text-xs md:px-2">过滤</span>
          <Switch
            :checked="swapTransactionSettings.filter"
            @update:checked="
              (value) =>
                botConfigStore.setTransactionSettings({
                  ...swapTransactionSettings,
                  filter: value,
                })
            "
          />
        </template>
        <Badge
          v-else
          :class="
            isHover
              ? 'bg-warning-500 [&:not(:disabled)]:hover:bg-warning-500'
              : 'bg-primary [&:not(:disabled)]:hover:bg-primary'
          "
        >
          {{ isHover ? 'Pause' : 'Active' }}
        </Badge>
      </template>
    </template>
    <template #swap>
      <component
        :is="currentComponent.swapAction"
        v-if="currentComponent && tokenData.poolInfo"
        v-bind="swapActionProps"
      />
      <SilkLoading v-else class="size-full" spinning />
    </template>
    <template #quickTrading>
      <component
        :is="currentComponent.swapAction"
        v-if="currentComponent && tokenData.poolInfo"
        v-bind="swapActionProps"
        quick-trading
      />
      <SilkLoading v-else class="size-full" spinning />
    </template>
    <template #transaction>
      <component
        :is="
          chain === 'ethereum' ? EthereumSwapTransaction : SolanaSwapTransaction
        "
        :chain="chain"
        :monitor-list="monitorList ?? []"
        :pair-address="pairAddress"
        :pool="pool"
        :wallet-address="transactionMaker"
        class="py-1"
        @update:hover-status="isHover = $event"
        @update:monitor-list="refetchMonitorList"
      />
    </template>
    <template #holder>
      <component
        :is="chain === 'ethereum' ? EthereumHolder : SolanaHolder"
        ref="holderRef"
        :chain="chain"
        :monitor-list="monitorList ?? []"
        :pair-address="pairAddress"
        :pool="pool"
        @holder-change="onHolderChange"
        @update:monitor-list="refetchMonitorList"
      />
    </template>
    <template #profit>
      <component
        :is="chain === 'ethereum' ? EthereumProfit : SolanaProfit"
        :chain="chain"
        :monitor-list="monitorList"
        :pair-address="pairAddress"
        :pool="pool"
        include-transactions
        @update:monitor-list="refetchMonitorList"
      />
    </template>
    <template #tokenManage>
      <component
        :is="chain === 'ethereum' ? EthereumTokenManage : SolanaTokenManage"
      />
    </template>
    <template #tokenInfo>
      <TokenInfo :pool class="px-10 py-5" />
      <SolanaTokenPoolInfo v-if="chain === 'solana'" :pool />
      <EthereumTokenPoolInfo
        v-else-if="'pair' in swapActionProps"
        :pair="swapActionProps.pair"
        :pool
        :token-info="swapActionProps.tokenInfo"
      />
    </template>
  </TransactionCard>
</template>
