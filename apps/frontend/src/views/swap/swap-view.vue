<script lang="ts" setup>
import { defineAsyncComponent, onBeforeUnmount, ref, watchEffect } from 'vue';

import {
  EthereumTokenBasicBlock,
  Resizable,
  SilkLogo,
  SilkNotifcationBar,
  SilkScrollbar,
  SilkSpinner,
  SolanaTokenBasicBlock,
  TokenBasicInfo,
} from '@silk/common-ui';
import { useIsMobile } from '@silk/hooks';
import { Logo } from '@silk/icons';
import { $t } from '@silk/locales';
import { preferences, usePreferences } from '@silk/preferences';
import { displayPrice } from '@silk/utils';

import { useSwapView, useSwapWebSocket } from '@/composables';
import { useWatchListStore } from '@/store';
import { useTitle } from '@vueuse/core';

const SwapChart = defineAsyncComponent(() => import('./swap-chart.vue'));
const SwapLogs = defineAsyncComponent(() => import('./swap-logs.vue'));

const watchListStore = useWatchListStore();
const { isMobile } = useIsMobile();

const { theme } = usePreferences();

const {
  chain,
  pairAddress,
  currentComponent,
  tokenData,
  swapInfoProps,
  swapActionProps,
} = useSwapView();

const { closeSolanaTransactionWebSocket } = useSwapWebSocket();

const activeTab = ref(isMobile.value ? 'swap' : 'transaction');

onBeforeUnmount(() => {
  closeSolanaTransactionWebSocket();
});

watchEffect(() => {
  const { poolInfo } = tokenData.value;
  const appName = preferences.app.name;
  useTitle(
    poolInfo
      ? `${poolInfo.baseToken.symbol} $${displayPrice(poolInfo.priceUsd)} - ${appName}`
      : appName,
  );
});
</script>

<template>
  <SilkNotifcationBar
    v-if="tokenData.poolInfo?.isCheat"
    :message="$t('swap.token.cheatMessage')"
  />
  <template v-if="isMobile">
    <div class="flex h-[50px] items-center gap-1">
      <SilkLogo :logo="Logo" :theme="theme" class="z-10" href="/" />
      <template v-if="tokenData.poolInfo">
        <SolanaTokenBasicBlock
          v-if="chain === 'solana'"
          :is-watch="watchListStore.isWatch"
          :pool="tokenData.poolInfo"
          @toggle-watch-item="watchListStore.toggleWatchItem"
        />
        <EthereumTokenBasicBlock
          v-else-if="chain === 'ethereum'"
          :is-watch="watchListStore.isWatch"
          :pool="tokenData.poolInfo"
          @toggle-watch-item="watchListStore.toggleWatchItem"
        />
      </template>
    </div>

    <template v-if="tokenData.poolInfo">
      <TokenBasicInfo :pool="tokenData.poolInfo" class="my-2" />
      <SwapChart
        v-if="tokenData.poolInfo.baseToken && tokenData.poolInfo.quoteToken"
        :active-tab
        :chain="chain"
        :pair-address="pairAddress"
        :pool="tokenData.poolInfo"
        :title="`${tokenData.poolInfo.baseToken.symbol}/${tokenData.poolInfo.quoteToken.symbol}`"
        class="flex-1"
      />
      <SwapLogs
        v-model:active-tab="activeTab"
        :chain="chain"
        :class="{ '!h-[288px] overflow-hidden': isMobile }"
        :pair-address="pairAddress"
        :pool="tokenData.poolInfo"
        :token-info="tokenData.tokenInfo"
      />
    </template>
  </template>
  <template v-else>
    <template v-if="tokenData.poolInfo && !tokenData.loading">
      <Resizable direction="vertical">
        <template #top>
          <SwapChart
            v-if="tokenData.poolInfo.baseToken && tokenData.poolInfo.quoteToken"
            :active-tab
            :chain="chain"
            :pair-address="pairAddress"
            :pool="tokenData.poolInfo"
            :title="`${tokenData.poolInfo.baseToken.symbol}/${tokenData.poolInfo.quoteToken.symbol}`"
          />
        </template>
        <template #bottom>
          <SwapLogs
            v-model:active-tab="activeTab"
            :chain="chain"
            :pair-address="pairAddress"
            :pool="tokenData.poolInfo"
            :token-info="tokenData.tokenInfo"
          />
        </template>
      </Resizable>
      <div
        v-if="currentComponent"
        :class="{ 'w-[clamp(328px,min(20%,380px),480px)]': !isMobile }"
        class="shrink-0 overflow-y-auto overflow-x-hidden"
      >
        <SilkScrollbar class="h-full">
          <component :is="currentComponent.swapInfo" v-bind="swapInfoProps" />
          <component
            :is="currentComponent.swapAction"
            v-bind="swapActionProps"
          />
        </SilkScrollbar>
      </div>
    </template>
    <div v-else-if="tokenData.loading">
      <SilkSpinner class="mx-auto my-auto" spinning />
    </div>
    <div v-else></div>
  </template>
</template>
