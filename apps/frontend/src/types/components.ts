import type { Chain } from '@silk/types';

type SupportedchainType = `${Chain}`;

enum ChainComponentName {
  HOLDER = 'holder',
  PROFIT = 'profit',
  SWAP_ACTION = 'swapAction',
  SWAP_INFO = 'swapInfo',
  TAG_HOLDER = 'tagHolder',
  TOKEN_MANAGE = 'tokenManage',
  TOP_HOLDER = 'topHolder',
  TRANSACTION_LIST = 'transactionList',
  WALLET_CONNECT = 'walletConnect',
}

type SupportedChainComponentName = `${ChainComponentName}`;

export type {
  Chain,
  ChainComponentName,
  SupportedChainComponentName,
  SupportedchainType,
};
