import type { SupportedchainType } from './components';

import type {
  EthereumTokenInfo,
  FormattedPoolInfo,
  PairInfo,
} from '@silk/types';

import type { Component } from 'vue';

export interface BaseSwapProps {
  pool: FormattedPoolInfo;
  compact?: boolean;
  inSettingsModal?: boolean;
  chain: SupportedchainType;
  quickTrading?: boolean;
}

export interface EthereumSwapProps extends BaseSwapProps {
  tokenInfo: EthereumTokenInfo;
  pair: PairInfo;
}

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface SolanaSwapProps extends BaseSwapProps {}

export interface SwapComponents {
  swapInfo: Component;
  swapAction: Component;
}

/**
 * 区块链特定组件属性类型
 * 根据区块链类型返回对应的属性类型
 */
export type BlockchainSpecificProps<T extends SupportedchainType> =
  T extends 'ethereum'
    ? EthereumSwapProps
    : T extends 'solana'
      ? SolanaSwapProps
      : never;
