import type {
  Chain,
  SupportedChainComponentName,
  SupportedchainType,
} from './components';

import type { Component } from 'vue';

export interface BlockchainComponentMeta {
  readonly chainType: Chain;
  readonly componentName: SupportedChainComponentName;
}

export type ComponentLoader = () => Promise<Component>;

export type ComponentMap = Record<
  SupportedchainType,
  Partial<Record<SupportedChainComponentName, ComponentLoader>>
>;

export interface ComponentFactory {
  /**
   * 根据区块链类型和组件名称获取组件
   * @param chainType 区块链类型
   * @param componentName 组件名称
   * @returns 异步加载的组件
   */
  getComponent<T extends Component>(
    chainType: Chain,
    componentName: SupportedChainComponentName,
  ): Promise<T>;

  /**
   * 注册组件到工厂
   * @param chainType 区块链类型
   * @param componentName 组件名称
   * @param component 组件加载器函数
   */
  registerComponent(
    chainType: Chain,
    componentName: SupportedChainComponentName,
    component: ComponentLoader,
  ): void;

  /**
   * 检查组件是否已注册
   * @param chainType 区块链类型
   * @param componentName 组件名称
   * @returns 是否已注册
   */
  hasComponent(
    chainType: Chain,
    componentName: SupportedChainComponentName,
  ): boolean;
}

/**
 * 组件加载选项接口
 * 定义加载组件时的配置选项
 */
export interface ComponentLoadOptions {
  /** 加载中显示的组件 */
  loadingComponent?: Component;
  /** 错误时显示的组件 */
  errorComponent?: Component;
  /** 加载超时时间(毫秒) */
  timeout?: number;
}

/**
 * 组件加载结果接口
 * 定义加载组件的结果状态
 */
export interface ComponentLoadResult<T extends Component = Component> {
  /** 加载的组件 */
  component: null | T;
  /** 是否正在加载 */
  isLoading: boolean;
  /** 加载错误信息 */
  error: Error | null;
  /** 重新加载组件的方法 */
  reload: () => Promise<void>;
}
