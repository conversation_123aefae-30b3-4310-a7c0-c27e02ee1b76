import type { EthereumSwapProps, SolanaSwapProps } from '@/types';

import { computed } from 'vue';
import { useRoute } from 'vue-router';

import { useIsMobile } from '@silk/hooks';
import { usePairStore, usePoolStore, useTokenStore } from '@silk/stores';

import { useSwapComponents, useSwapQuery } from '@/composables';

const { isMobile } = useIsMobile();

export function useSwapView() {
  const route = useRoute();
  const chain = route.params.chain as 'ethereum' | 'solana';
  const pairAddress = route.params.pairAddress as string;

  const tokenStore = useTokenStore();
  const pairStore = usePairStore();
  const poolStore = usePoolStore();

  const {
    ethereumSwapData,
    ethereumSwapLoading,
    solanaSwapData,
    solanaSwapLoading,
    solanaDexscreenerData,
  } = useSwapQuery(chain, pairAddress);

  const currentComponent = useSwapComponents(chain);
  pairStore.setActivePair(pairAddress);

  const tokenData = computed(() => {
    if (chain === 'ethereum') {
      return {
        pairInfo:
          pairStore.getPair(pairAddress) ?? ethereumSwapData.value?.pairInfo,
        poolInfo:
          poolStore.getPool(pairAddress) ?? ethereumSwapData.value?.poolInfo,
        tokenInfo:
          tokenStore.getEthereumToken(pairAddress) ??
          ethereumSwapData.value?.tokenInfo,
        loading: ethereumSwapLoading.value,
      };
    } else {
      const dexPoolInfo = solanaDexscreenerData.value;
      const poolInfo =
        poolStore.getPool(pairAddress) ?? solanaSwapData.value?.poolInfo;
      if (dexPoolInfo && poolInfo) {
        poolInfo.images ??= {};
        if (dexPoolInfo.images?.header) {
          poolInfo.images.header = dexPoolInfo.images.header;
        }
        if (dexPoolInfo.images?.imageUrl) {
          poolInfo.images.imageUrl = dexPoolInfo.images.imageUrl;
        }
        poolInfo.volume ??= { h1: 0, h6: 0, h24: 0, m5: 0 };
        if (dexPoolInfo.volume?.h24) {
          poolInfo.volume.h24 = dexPoolInfo.volume.h24;
        }
        poolInfo.socials ??= {};
        if (dexPoolInfo?.socials?.telegram) {
          poolInfo.socials.telegram = dexPoolInfo.socials.telegram;
        }
        if (dexPoolInfo?.socials?.twitter) {
          poolInfo.socials.twitter = dexPoolInfo.socials.twitter;
        }
        if (dexPoolInfo?.socials?.website) {
          poolInfo.socials.website = dexPoolInfo.socials.website;
        }
      }
      return {
        poolInfo,
        loading: solanaSwapLoading.value,
      };
    }
  });

  const swapInfoProps = computed(() => {
    return chain === 'ethereum'
      ? ({
          pair: tokenData.value.pairInfo,
          pool: tokenData.value.poolInfo,
          tokenInfo: tokenData.value.tokenInfo,
        } as EthereumSwapProps)
      : ({
          pool: tokenData.value.poolInfo,
        } as SolanaSwapProps);
  });

  const swapActionProps = computed(() => {
    return (
      chain === 'ethereum'
        ? {
            pair: tokenData.value.pairInfo,
            pool: tokenData.value.poolInfo,
            tokenInfo: tokenData.value.tokenInfo,
            compact: isMobile.value,
            chain,
          }
        : {
            pool: tokenData.value.poolInfo,
            compact: isMobile.value,
            chain,
            quickTrading: false,
          }
    ) as EthereumSwapProps | SolanaSwapProps;
  });

  return {
    chain,
    pairAddress,
    currentComponent,
    tokenData,
    swapInfoProps,
    swapActionProps,
  };
}
