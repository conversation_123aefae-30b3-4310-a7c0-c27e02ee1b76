import type { SwapComponents } from '@/types/swap';

import { computed, defineAsyncComponent } from 'vue';

export function useSwapComponents(chain: 'ethereum' | 'solana') {
  const components: Record<string, SwapComponents> = {
    ethereum: {
      swapInfo: defineAsyncComponent(
        () => import('@/views/swap/module/info/ethereum-swap-info.vue'),
      ),
      swapAction: defineAsyncComponent(
        () => import('@/views/swap/module/actions/ethereum-swap-action.vue'),
      ),
    },
    solana: {
      swapInfo: defineAsyncComponent(
        () => import('@/views/swap/module/info/solana-swap-info.vue'),
      ),
      swapAction: defineAsyncComponent(
        () => import('@/views/swap/module/actions/solana-swap-action.vue'),
      ),
    },
  };

  return computed(() => components[chain]);
}
