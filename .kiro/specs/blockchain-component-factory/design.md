# Design Document: 区块链组件工厂

## Overview

本设计文档详细描述了一个灵活、可扩展的区块链组件工厂系统，用于在 Silkbot 应用中根据不同的区块链类型（如 Ethereum 和 Solana）动态渲染相应的组件。该设计采用工厂模式和策略模式的组合，结合 Vue 3 的 Composition API 和 TypeScript 的类型系统，提供一个类型安全、可扩展且性能优化的解决方案。

## Architecture

系统架构采用分层设计，主要包含以下几个部分：

1. **组件注册层**：负责注册和管理不同区块链的组件实现
2. **组件工厂层**：根据区块链类型选择并返回相应的组件
3. **组件接口层**：定义各类组件必须实现的接口
4. **组件实现层**：各区块链特定的组件实现
5. **组件消费层**：应用中使用组件工厂的部分

整体架构如下图所示：

```mermaid
graph TD
    A[应用组件] --> B[组件工厂]
    B --> C[组件注册中心]
    C --> D1[Ethereum组件]
    C --> D2[Solana组件]
    C --> D3[其他区块链组件]
    B --> E[懒加载控制器]
    E --> F[组件加载状态管理]
```

## Components and Interfaces

### 1. 区块链类型枚举

```typescript
// types/blockchain.ts
export enum BlockchainType {
  ETHEREUM = 'ethereum',
  SOLANA = 'solana',
  // 未来可扩展其他区块链
}

export type SupportedBlockchainType = `${BlockchainType}`; 
```

### 2. 组件接口定义

为了确保不同区块链的组件实现相同的接口，我们定义以下基础接口：

```typescript
// types/component-interfaces.ts
import { Component } from 'vue';
import { BlockchainType } from './blockchain';

// 基础组件接口
export interface BlockchainComponent {
  // 组件元数据
  readonly blockchainType: BlockchainType;
  readonly componentName: string;
}

// 组件工厂接口
export interface ComponentFactory {
  // 根据区块链类型和组件名称获取组件
  getComponent<T extends Component>(
    blockchainType: BlockchainType, 
    componentName: string
  ): Promise<T>;
  
  // 注册组件
  registerComponent(
    blockchainType: BlockchainType,
    componentName: string,
    component: () => Promise<Component>
  ): void;
  
  // 检查组件是否已注册
  hasComponent(
    blockchainType: BlockchainType,
    componentName: string
  ): boolean;
}

// 组件映射类型
export type ComponentMap = Record<
  BlockchainType, 
  Record<string, () => Promise<Component>>
>;
```

### 3. 组件工厂实现

```typescript
// factories/blockchain-component-factory.ts
import { Component, defineAsyncComponent, shallowRef } from 'vue';
import { BlockchainType, ComponentFactory, ComponentMap } from '../types';

export class BlockchainComponentFactory implements ComponentFactory {
  private componentMap: ComponentMap = {
    [BlockchainType.ETHEREUM]: {},
    [BlockchainType.SOLANA]: {},
  };
  
  // 单例模式
  private static instance: BlockchainComponentFactory;
  
  private constructor() {}
  
  public static getInstance(): BlockchainComponentFactory {
    if (!BlockchainComponentFactory.instance) {
      BlockchainComponentFactory.instance = new BlockchainComponentFactory();
    }
    return BlockchainComponentFactory.instance;
  }
  
  public registerComponent(
    blockchainType: BlockchainType,
    componentName: string,
    component: () => Promise<Component>
  ): void {
    if (!this.componentMap[blockchainType]) {
      this.componentMap[blockchainType] = {};
    }
    
    this.componentMap[blockchainType][componentName] = component;
  }
  
  public hasComponent(
    blockchainType: BlockchainType,
    componentName: string
  ): boolean {
    return !!(
      this.componentMap[blockchainType] && 
      this.componentMap[blockchainType][componentName]
    );
  }
  
  public async getComponent<T extends Component>(
    blockchainType: BlockchainType,
    componentName: string
  ): Promise<T> {
    if (!this.hasComponent(blockchainType, componentName)) {
      throw new Error(
        `Component "${componentName}" for blockchain "${blockchainType}" not registered`
      );
    }
    
    try {
      const componentLoader = this.componentMap[blockchainType][componentName];
      const component = await componentLoader();
      return component as T;
    } catch (error) {
      console.error(
        `Failed to load component "${componentName}" for blockchain "${blockchainType}"`,
        error
      );
      throw error;
    }
  }
}
```

### 4. Vue Composable 封装

为了方便在 Vue 组件中使用组件工厂，我们创建一个 Composable：

```typescript
// composables/use-blockchain-component.ts
import { Component, defineAsyncComponent, shallowRef, ref, Ref } from 'vue';
import { BlockchainType } from '../types/blockchain';
import { BlockchainComponentFactory } from '../factories/blockchain-component-factory';

export interface UseBlockchainComponentOptions {
  loadingComponent?: Component;
  errorComponent?: Component;
  timeout?: number;
}

export function useBlockchainComponent(
  blockchainType: Ref<BlockchainType>,
  componentName: string,
  options: UseBlockchainComponentOptions = {}
) {
  const factory = BlockchainComponentFactory.getInstance();
  const isLoading = ref(false);
  const error = ref<Error | null>(null);
  
  // 使用 shallowRef 优化性能
  const component = shallowRef<Component | null>(null);
  
  const loadComponent = async () => {
    if (!factory.hasComponent(blockchainType.value, componentName)) {
      error.value = new Error(
        `Component "${componentName}" for blockchain "${blockchainType.value}" not registered`
      );
      return;
    }
    
    isLoading.value = true;
    error.value = null;
    
    try {
      const loadedComponent = await factory.getComponent(
        blockchainType.value,
        componentName
      );
      component.value = loadedComponent;
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(String(err));
    } finally {
      isLoading.value = false;
    }
  };
  
  // 当区块链类型变化时重新加载组件
  watch(blockchainType, () => {
    loadComponent();
  }, { immediate: true });
  
  // 创建异步组件
  const asyncComponent = computed(() => {
    if (!component.value) return null;
    
    return defineAsyncComponent({
      loader: () => Promise.resolve(component.value!),
      loadingComponent: options.loadingComponent,
      errorComponent: options.errorComponent,
      timeout: options.timeout,
    });
  });
  
  return {
    component: asyncComponent,
    isLoading,
    error,
    reload: loadComponent,
  };
}
```

### 5. 组件注册机制

```typescript
// plugins/register-blockchain-components.ts
import { App } from 'vue';
import { BlockchainType } from '../types/blockchain';
import { BlockchainComponentFactory } from '../factories/blockchain-component-factory';

// 以插件形式提供组件注册功能
export const BlockchainComponentsPlugin = {
  install(app: App) {
    const factory = BlockchainComponentFactory.getInstance();
    
    // 注册 Ethereum 组件
    factory.registerComponent(
      BlockchainType.ETHEREUM,
      'WalletConnect',
      () => import('../components/ethereum/WalletConnect.vue')
    );
    
    factory.registerComponent(
      BlockchainType.ETHEREUM,
      'TransactionList',
      () => import('../components/ethereum/TransactionList.vue')
    );
    
    // 注册 Solana 组件
    factory.registerComponent(
      BlockchainType.SOLANA,
      'WalletConnect',
      () => import('../components/solana/WalletConnect.vue')
    );
    
    factory.registerComponent(
      BlockchainType.SOLANA,
      'TransactionList',
      () => import('../components/solana/TransactionList.vue')
    );
    
    // 将工厂实例添加到全局属性中
    app.config.globalProperties.$blockchainComponentFactory = factory;
    
    // 提供工厂实例
    app.provide('blockchainComponentFactory', factory);
  }
};
```

## Data Models

本功能主要涉及组件的动态加载和管理，不需要特定的数据模型。主要的数据结构是组件映射表，如上面代码中的 `ComponentMap` 类型。

## Error Handling

错误处理策略包括：

1. **组件注册错误**：当注册无效组件时，提供明确的错误信息
2. **组件加载错误**：当组件加载失败时，捕获并显示错误，同时提供回退组件
3. **未知区块链类型**：当请求未注册的区块链类型组件时，提供明确的错误信息和回退机制

错误处理流程：

```mermaid
flowchart TD
    A[请求组件] --> B{组件是否注册?}
    B -->|是| C[加载组件]
    B -->|否| D[抛出未注册错误]
    C --> E{加载成功?}
    E -->|是| F[返回组件]
    E -->|否| G[抛出加载错误]
    G --> H[显示错误组件]
    D --> I[显示回退组件]
```

## Testing Strategy

测试策略分为以下几个层次：

### 1. 单元测试

- 测试组件工厂的注册、检查和获取功能
- 测试 Composable 的响应性和错误处理
- 测试各区块链特定组件的独立功能

### 2. 集成测试

- 测试组件工厂与 Vue 应用的集成
- 测试区块链切换时的组件切换逻辑
- 测试懒加载机制的性能表现

### 3. 端到端测试

- 测试用户切换区块链时的 UI 响应
- 测试在不同网络条件下的加载行为
- 测试错误状态下的用户体验

### 测试工具和模拟

- 使用 Vitest 进行单元和集成测试
- 使用 Vue Test Utils 测试组件行为
- 创建模拟区块链环境进行测试
- 使用 MSW (Mock Service Worker) 模拟网络请求

## 实现注意事项

1. **性能优化**：
   - 使用 `shallowRef` 而非 `ref` 来存储组件引用，减少不必要的响应式开销
   - 组件懒加载以减少初始加载时间
   - 预加载常用组件以提高用户体验

2. **类型安全**：
   - 利用 TypeScript 泛型确保组件类型安全
   - 提供完整的类型定义和接口

3. **可扩展性**：
   - 设计支持轻松添加新的区块链类型
   - 组件注册机制支持运行时动态注册

4. **用户体验**：
   - 提供加载状态和错误状态的视觉反馈
   - 平滑的组件切换动画
   - 保持用户状态在组件切换过程中
