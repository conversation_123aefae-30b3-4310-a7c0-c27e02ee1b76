import type { ComputedRef } from 'vue';

interface BasicOption {
  disabled?: boolean;
  label: ComputedRef<string> | string;
  value: string;
}

type SelectOption = BasicOption;

type TabOption = BasicOption;

interface BasicUserInfo {
  id: number;
}

type ClassType = Array<object | string> | object | string;

enum Chain {
  ETHEREUM = 'ethereum',
  SOLANA = 'solana',
}

export type {
  BasicOption,
  BasicUserInfo,
  Chain,
  ClassType,
  SelectOption,
  TabOption,
};
